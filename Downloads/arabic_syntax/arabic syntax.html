<html lang="ar" dir="rtl"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النحو الكافي - تطبيق قواعد اللغة العربية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&amp;display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f5f2;
            color: #333;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #3a6186, #89253e);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .app-button {
            transition: all 0.3s ease;
        }
        .app-button:hover {
            transform: scale(1.05);
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.absolute{position:absolute}.relative{position:relative}.bottom-0{bottom:0px}.left-0{left:0px}.right-0{right:0px}.top-0{top:0px}.mx-auto{margin-left:auto;margin-right:auto}.mb-10{margin-bottom:2.5rem}.mb-12{margin-bottom:3rem}.mb-16{margin-bottom:4rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-3{margin-left:0.75rem}.mt-10{margin-top:2.5rem}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-1{height:0.25rem}.h-10{height:2.5rem}.h-16{height:4rem}.h-20{height:5rem}.h-3{height:0.75rem}.h-4{height:1rem}.h-40{height:10rem}.h-6{height:1.5rem}.h-64{height:16rem}.h-8{height:2rem}.h-\[500px\]{height:500px}.h-full{height:100%}.w-1\/2{width:50%}.w-10{width:2.5rem}.w-16{width:4rem}.w-2\/3{width:66.666667%}.w-20{width:5rem}.w-3{width:0.75rem}.w-3\/4{width:75%}.w-40{width:10rem}.w-6{width:1.5rem}.w-64{width:16rem}.w-8{width:2rem}.w-full{width:100%}.max-w-2xl{max-width:42rem}.max-w-3xl{max-width:48rem}.max-w-xs{max-width:20rem}.flex-shrink-0{flex-shrink:0}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-10{gap:2.5rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-6 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1.5rem * var(--tw-space-x-reverse));margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-x-reverse > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:1}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.rounded{border-radius:0.25rem}.rounded-2xl{border-radius:1rem}.rounded-3xl{border-radius:1.5rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.rounded-b-lg{border-bottom-right-radius:0.5rem;border-bottom-left-radius:0.5rem}.rounded-t-lg{border-top-left-radius:0.5rem;border-top-right-radius:0.5rem}.border-2{border-width:2px}.border-8{border-width:8px}.border-t{border-top-width:1px}.border-gray-800{--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.bg-\[\#3a6186\]{--tw-bg-opacity:1;background-color:rgb(58 97 134 / var(--tw-bg-opacity, 1))}.bg-\[\#89253e\]{--tw-bg-opacity:1;background-color:rgb(137 37 62 / var(--tw-bg-opacity, 1))}.bg-\[\#d4a017\]{--tw-bg-opacity:1;background-color:rgb(212 160 23 / var(--tw-bg-opacity, 1))}.bg-\[\#e6f0ff\]{--tw-bg-opacity:1;background-color:rgb(230 240 255 / var(--tw-bg-opacity, 1))}.bg-\[\#e6fff0\]{--tw-bg-opacity:1;background-color:rgb(230 255 240 / var(--tw-bg-opacity, 1))}.bg-\[\#f0e6ff\]{--tw-bg-opacity:1;background-color:rgb(240 230 255 / var(--tw-bg-opacity, 1))}.bg-\[\#fff5e6\]{--tw-bg-opacity:1;background-color:rgb(255 245 230 / var(--tw-bg-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-300{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))}.bg-gray-400{--tw-bg-opacity:1;background-color:rgb(156 163 175 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-gradient-to-b{background-image:linear-gradient(to bottom, var(--tw-gradient-stops))}.from-\[\#f0e6ff\]{--tw-gradient-from:#f0e6ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(240 230 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-\[\#e6f0ff\]{--tw-gradient-to:#e6f0ff var(--tw-gradient-to-position)}.p-2{padding:0.5rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.px-8{padding-left:2rem;padding-right:2rem}.py-12{padding-top:3rem;padding-bottom:3rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-20{padding-top:5rem;padding-bottom:5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.pb-8{padding-bottom:2rem}.pt-6{padding-top:1.5rem}.text-center{text-align:center}.text-right{text-align:right}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.leading-relaxed{line-height:1.625}.text-\[\#2e8b57\]{--tw-text-opacity:1;color:rgb(46 139 87 / var(--tw-text-opacity, 1))}.text-\[\#3a6186\]{--tw-text-opacity:1;color:rgb(58 97 134 / var(--tw-text-opacity, 1))}.text-\[\#89253e\]{--tw-text-opacity:1;color:rgb(137 37 62 / var(--tw-text-opacity, 1))}.text-\[\#d4a017\]{--tw-text-opacity:1;color:rgb(212 160 23 / var(--tw-text-opacity, 1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-inner{--tw-shadow:inset 0 2px 4px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:inset 0 2px 4px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition{transition-property:color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.hover\:bg-gray-900:hover{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.hover\:bg-white:hover{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.hover\:bg-opacity-10:hover{--tw-bg-opacity:0.1}.hover\:bg-opacity-90:hover{--tw-bg-opacity:0.9}.hover\:text-\[\#89253e\]:hover{--tw-text-opacity:1;color:rgb(137 37 62 / var(--tw-text-opacity, 1))}.hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}@media (min-width: 768px){.md\:mb-0{margin-bottom:0px}.md\:flex{display:flex}.md\:hidden{display:none}.md\:w-1\/2{width:50%}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:flex-row{flex-direction:row}.md\:px-12{padding-left:3rem;padding-right:3rem}.md\:text-4xl{font-size:2.25rem;line-height:2.5rem}.md\:text-5xl{font-size:3rem;line-height:1}}@media (min-width: 1024px){.lg\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}</style></head>
<body class="overflow-x-hidden">
    <!-- Navigation -->
    <nav class="bg-white shadow-md py-4 px-6 md:px-12 flex justify-between items-center">
        <div class="flex items-center">
            <div class="text-3xl font-bold text-[#89253e]">النحو الكافي</div>
        </div>
        <div class="hidden md:flex space-x-reverse space-x-8">
            <a href="#features" class="font-medium hover:text-[#89253e] transition">المميزات</a>
            <a href="#about" class="font-medium hover:text-[#89253e] transition">عن التطبيق</a>
            <a href="#download" class="font-medium hover:text-[#89253e] transition">تحميل</a>
        </div>
        <div class="md:hidden">
            <button id="menu-toggle" class="focus:outline-none">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                </svg>
            </button>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md py-4 px-6">
        <a href="#features" class="block py-2 font-medium hover:text-[#89253e] transition">المميزات</a>
        <a href="#about" class="block py-2 font-medium hover:text-[#89253e] transition">عن التطبيق</a>
        <a href="#download" class="block py-2 font-medium hover:text-[#89253e] transition">تحميل</a>
    </div>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20 px-6 md:px-12">
        <div class="container mx-auto flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-10 md:mb-0">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">إتقان قواعد اللغة العربية أصبح بين يديك!</h1>
                <p class="text-xl mb-8">تطبيق "النحو الكافي" هو دليلك الشامل لتعلم وفهم قواعد النحو العربي بسهولة ويسر.</p>
                <div class="flex flex-wrap gap-4">
                    <a href="#download" class="app-button bg-white text-[#89253e] font-bold py-3 px-8 rounded-full shadow-lg hover:bg-opacity-90 transition">
                        حمّل التطبيق الآن!
                    </a>
                    <a href="#about" class="app-button border-2 border-white py-3 px-8 rounded-full hover:bg-white hover:bg-opacity-10 transition">
                        اكتشف المزيد
                    </a>
                </div>
            </div>
            <div class="md:w-1/2 flex justify-center">
                <div class="relative w-64 h-[500px] bg-white rounded-3xl shadow-2xl overflow-hidden border-8 border-gray-800">
                    <div class="absolute top-0 left-0 right-0 h-6 bg-gray-800 rounded-t-lg"></div>
                    <div class="h-full pt-6 bg-gradient-to-b from-[#f0e6ff] to-[#e6f0ff] flex items-center justify-center">
                        <svg class="w-40 h-40" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="50" cy="50" r="45" fill="#89253e"></circle>
                            <text x="50" y="60" font-family="Tajawal" font-size="20" fill="white" text-anchor="middle" font-weight="bold">النحو الكافي</text>
                            <path d="M30,35 Q50,20 70,35" stroke="white" stroke-width="3" fill="none"></path>
                            <path d="M30,50 Q50,35 70,50" stroke="white" stroke-width="3" fill="none"></path>
                            <path d="M30,65 Q50,50 70,65" stroke="white" stroke-width="3" fill="none"></path>
                        </svg>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 h-6 bg-gray-800 rounded-b-lg flex justify-center items-center">
                        <div class="w-16 h-1 bg-gray-400 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 px-6 md:px-12 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-12 text-center">ما هو تطبيق "النحو الكافي"؟</h2>
            <div class="max-w-3xl mx-auto">
                <p class="text-lg leading-relaxed mb-6">
                    "النحو الكافي" هو تطبيق تعليمي مبتكر مصمم لمساعدة الناطقين باللغة العربية ودارسيها على فهم واستيعاب قواعد النحو العربي بشكل مبسط وتفاعلي.
                </p>
                <p class="text-lg leading-relaxed">
                    سواء كنت طالبًا تسعى لتحسين درجاتك، أو محترفًا ترغب في صقل مهاراتك اللغوية، أو ببساطة شغوفًا باللغة العربية، فإن "النحو الكافي" يوفر لك الأدوات والموارد التي تحتاجها.
                </p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 px-6 md:px-12 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-4 text-center">أقسام التطبيق ومميزاته</h2>
            <p class="text-xl text-center text-gray-600 mb-16 max-w-3xl mx-auto">اكتشف الأدوات المتنوعة التي يقدمها تطبيق النحو الكافي لمساعدتك على إتقان قواعد اللغة العربية</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                    <div class="w-16 h-16 bg-[#f0e6ff] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-8 h-8 text-[#89253e]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-center">الدروس</h3>
                    <p class="text-gray-600 text-center">
                        تعلم علم النحو وأقسام الكلام بطريقة منظمة وسهلة الفهم. يقدم التطبيق شروحات وافية للقواعد الأساسية والمتقدمة.
                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                    <div class="w-16 h-16 bg-[#e6f0ff] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-8 h-8 text-[#3a6186]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-center">نماذج إعرابية</h3>
                    <p class="text-gray-600 text-center">
                        تدرب على إعراب الأفعال، المبتدأ والخبر، وغيرها من الجمل والتراكيب اللغوية. يوفر التطبيق أمثلة متنوعة مع حلولها المفصلة.
                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                    <div class="w-16 h-16 bg-[#fff5e6] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-8 h-8 text-[#d4a017]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-center">البحث في المعجم</h3>
                    <p class="text-gray-600 text-center">
                        اكشف في المعجم عن أي كلمة لفهم معناها واستخداماتها المختلفة. أداة سريعة وفعالة لزيادة ثروتك اللغوية.
                    </p>
                </div>

                <!-- Feature 4 -->
                <div class="feature-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                    <div class="w-16 h-16 bg-[#e6fff0] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-8 h-8 text-[#2e8b57]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-center">اختبر نفسك</h3>
                    <p class="text-gray-600 text-center">
                        اختبر نفسك وقيم مدى فهمك وحفظك للقواعد من خلال مجموعة متنوعة من التمارين والاختبارات التفاعلية.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-20 px-6 md:px-12 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center">لماذا تختار "النحو الكافي"؟</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <!-- Reason 1 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-[#f0e6ff] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-10 h-10 text-[#89253e]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">شروحات مبسطة</h3>
                    <p class="text-gray-600">
                        نقوم بتبسيط قواعد النحو المعقدة إلى دروس سهلة الفهم والاستيعاب.
                    </p>
                </div>

                <!-- Reason 2 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-[#e6f0ff] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-10 h-10 text-[#3a6186]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">أمثلة عملية</h3>
                    <p class="text-gray-600">
                        تعلم من خلال أمثلة واقعية وعبارات عربية شائعة الاستخدام.
                    </p>
                </div>

                <!-- Reason 3 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-[#fff5e6] rounded-full flex items-center justify-center mb-6 mx-auto">
                        <svg class="w-10 h-10 text-[#d4a017]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">تعلم تفاعلي</h3>
                    <p class="text-gray-600">
                        شارك في الاختبارات والتمارين لتعزيز فهمك وترسيخ المعلومات.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- App Screenshots -->
    <section class="py-20 px-6 md:px-12 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center">لقطات من التطبيق</h2>
            
            <div class="flex overflow-x-auto pb-8 space-x-reverse space-x-6">
                <!-- Screenshot 1 -->
                <div class="flex-shrink-0 w-64">
                    <div class="bg-white p-2 rounded-3xl shadow-lg">
                        <div class="bg-[#f0e6ff] rounded-2xl h-[500px] flex items-center justify-center">
                            <div class="text-center p-6">
                                <h3 class="text-2xl font-bold mb-4">الدروس</h3>
                                <div class="w-full h-64 bg-white rounded-lg shadow-inner p-4 mb-4">
                                    <div class="h-6 w-3/4 bg-gray-200 rounded mb-4"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-2/3 bg-gray-200 rounded mb-6"></div>
                                    <div class="h-6 w-1/2 bg-[#89253e] rounded mb-4"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                </div>
                                <div class="flex justify-center space-x-reverse space-x-2">
                                    <div class="w-3 h-3 rounded-full bg-[#89253e]"></div>
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screenshot 2 -->
                <div class="flex-shrink-0 w-64">
                    <div class="bg-white p-2 rounded-3xl shadow-lg">
                        <div class="bg-[#e6f0ff] rounded-2xl h-[500px] flex items-center justify-center">
                            <div class="text-center p-6">
                                <h3 class="text-2xl font-bold mb-4">نماذج إعرابية</h3>
                                <div class="w-full h-64 bg-white rounded-lg shadow-inner p-4 mb-4">
                                    <div class="h-8 w-full bg-[#3a6186] rounded mb-4 flex items-center justify-center">
                                        <div class="h-4 w-1/2 bg-white rounded"></div>
                                    </div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-4 w-2/3 bg-gray-200 rounded mb-3"></div>
                                    <div class="h-8 w-1/2 bg-[#3a6186] rounded mx-auto"></div>
                                </div>
                                <div class="flex justify-center space-x-reverse space-x-2">
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                    <div class="w-3 h-3 rounded-full bg-[#3a6186]"></div>
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screenshot 3 -->
                <div class="flex-shrink-0 w-64">
                    <div class="bg-white p-2 rounded-3xl shadow-lg">
                        <div class="bg-[#fff5e6] rounded-2xl h-[500px] flex items-center justify-center">
                            <div class="text-center p-6">
                                <h3 class="text-2xl font-bold mb-4">اختبر نفسك</h3>
                                <div class="w-full h-64 bg-white rounded-lg shadow-inner p-4 mb-4">
                                    <div class="h-6 w-3/4 bg-gray-200 rounded mb-6"></div>
                                    <div class="h-10 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-10 w-full bg-[#d4a017] rounded mb-3"></div>
                                    <div class="h-10 w-full bg-gray-200 rounded mb-3"></div>
                                    <div class="h-10 w-full bg-gray-200 rounded mb-3"></div>
                                </div>
                                <div class="flex justify-center space-x-reverse space-x-2">
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                    <div class="w-3 h-3 rounded-full bg-gray-300"></div>
                                    <div class="w-3 h-3 rounded-full bg-[#d4a017]"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="py-20 px-6 md:px-12 hero-gradient text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">حمّل التطبيق الآن!</h2>
            <p class="text-xl mb-10 max-w-2xl mx-auto">ابدأ رحلتك في إتقان قواعد اللغة العربية مع تطبيق "النحو الكافي" المتاح على جميع الأجهزة.</p>
            
            <div class="flex flex-wrap justify-center gap-6">
                <!-- App Store Button -->
                <a href="#" class="app-button bg-black text-white flex items-center px-8 py-3 rounded-xl hover:bg-gray-900 transition">
                    <svg class="w-8 h-8 ml-3" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.5 3C14.76 3 13.09 3.81 12 5.09C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.42 2 8.5C2 12.28 5.4 15.36 10.55 20.04L12 21.35L13.45 20.03C18.6 15.36 22 12.28 22 8.5C22 5.42 19.58 3 16.5 3ZM12.1 18.55L12 18.65L11.9 18.55C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5C9.04 5 10.54 5.99 11.07 7.36H12.94C13.46 5.99 14.96 5 16.5 5C18.5 5 20 6.5 20 8.5C20 11.39 16.86 14.24 12.1 18.55Z"></path>
                    </svg>
                    <div class="text-right">
                        <div class="text-xs">تحميل من</div>
                        <div class="text-xl font-semibold">App Store</div>
                    </div>
                </a>
                
                <!-- Google Play Button -->
                <a href="#" class="app-button bg-black text-white flex items-center px-8 py-3 rounded-xl hover:bg-gray-900 transition">
                    <svg class="w-8 h-8 ml-3" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.18 20.83C3.05 20.73 3 20.59 3 20.4V3.6C3 3.41 3.05 3.27 3.18 3.17L12 12L3.18 20.83Z"></path>
                        <path d="M13.42 13.42L4.6 22.25C4.72 22.31 4.85 22.35 5 22.35C5.15 22.35 5.29 22.31 5.4 22.25L15.82 16.18L13.42 13.42Z"></path>
                        <path d="M19.24 10.18L15.82 7.82L13.42 10.58L15.82 13.34L19.24 10.98C19.66 10.68 19.87 10.35 19.87 10C19.87 9.65 19.66 9.32 19.24 9.02V10.18Z"></path>
                        <path d="M4.6 1.75L13.42 10.58L15.82 7.82L5.4 1.75C5.29 1.69 5.15 1.65 5 1.65C4.85 1.65 4.72 1.69 4.6 1.75Z"></path>
                    </svg>
                    <div class="text-right">
                        <div class="text-xs">تحميل من</div>
                        <div class="text-xl font-semibold">Google Play</div>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 px-6 md:px-12">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-8 md:mb-0">
                    <h3 class="text-2xl font-bold mb-4">النحو الكافي</h3>
                    <p class="max-w-xs text-gray-400">تطبيق شامل لتعلم قواعد اللغة العربية بطريقة سهلة وممتعة.</p>
                </div>
                
                <div class="mb-8 md:mb-0">
                    <h4 class="text-lg font-bold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition">الرئيسية</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white transition">عن التطبيق</a></li>
                        <li><a href="#features" class="text-gray-400 hover:text-white transition">المميزات</a></li>
                        <li><a href="#download" class="text-gray-400 hover:text-white transition">تحميل</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-bold mb-4">تواصل معنا</h4>
                    <div class="flex space-x-reverse space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.477 2 2 6.477 2 12c0 5.523 4.477 10 10 10s10-4.477 10-10c0-5.523-4.477-10-10-10zm4.89 6.41c.69.007 1.31.438 1.54 1.084l.21.56-1.97.53c-.12-.44-.51-.76-.97-.76-.55 0-1 .45-1 1s.45 1 1 1c.46 0 .85-.32.97-.76l1.97.53-.21.56c-.23.646-.85 1.077-1.54 1.084-.5.007-.98-.18-1.33-.52-.35-.34-.55-.81-.55-1.3 0-.49.2-.96.55-1.3.35-.34.83-.527 1.33-.52zM12 15c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3zm-4.89-2.59c-.69-.007-1.31-.438-1.54-1.084l-.21-.56 1.97-.53c.12.44.51.76.97.76.55 0 1-.45 1-1s-.45-1-1-1c-.46 0-.85.32-.97.76l-1.97-.53.21-.56c.23-.646.85-1.077 1.54-1.084.5-.007.98.18 1.33.52.35.34.55.81.55 1.3 0 .49-.2.96-.55 1.3-.35.34-.83.527-1.33.52z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15h-2v-6h2v6zm-1-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm8 7h-2v-3c0-.55-.45-1-1-1s-1 .45-1 1v3h-2v-6h2v1.1c.17-.31.48-.6 1-.6 1.66 0 3 1.34 3 3v2.5z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-10 pt-6 text-center text-gray-500">
                <p>© 2023 النحو الكافي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'94a1b4a8972de1d1',t:'MTc0ODk4MDA4Mi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"></iframe>

</body></html>