<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق النحو الكافي</title>
    <!-- Chosen Palette: Warm Neutrals with Teal Accent -->
    <!-- Application Structure Plan: A single-page application (SPA) designed to feel like a native mobile app. The structure uses a fixed header and a fixed bottom navigation bar for primary navigation between main sections (Home, Search, Progress, Arena). The main content area dynamically swaps views (e.g., home grid, lesson view, search results) without page reloads. This was chosen to mirror the multi-screen flow of the source wireframe in a fluid, web-friendly way that is intuitive on both mobile and desktop. User interaction is centered around clicking cards and navigation items, triggering JavaScript functions to manage view visibility and content updates. -->
    <!-- Visualization & Content Choices: 
        - Home (Organize): A Tailwind CSS grid of clickable cards for grammar chapters. Interaction: click to show the lesson view. Justification: Visually appealing and common for topic selection.
        - Lesson (Inform/Test): Tabbed interface (HTML buttons + JS). Interaction: click tabs to switch between Explanation, Quiz, and Examples. Justification: Organizes related content cleanly within one view.
        - Quiz (Test): Interactive MCQs rendered one by one with JS from a data array. Interaction: select option, click check. Justification: Provides focused, step-by-step assessment.
        - Progress (Inform): A Chart.js doughnut chart to visualize user level. Viz: Chart.js/Canvas. Interaction: Static visualization of progress data. Justification: A quick, engaging visual summary of achievement. The canvas is in a responsive container.
        - Search (Organize/Find): Live search input filtering a JS array of terms. Interaction: Real-time filtering on keyup. Justification: Provides immediate, useful results for users needing quick answers.
        - All icons are Unicode characters as per the wireframe to avoid images/SVG. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            transition: background-color 0.3s;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
            animation: fadeIn 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .splash-screen {
            animation: fadeOut 0.5s 2s forwards;
        }
        @keyframes fadeOut {
            to {
                opacity: 0;
                visibility: hidden;
            }
        }
        .bottom-nav-btn.active {
            color: #0d9488;
        }
        .lesson-tab.active {
            border-color: #0d9488;
            background-color: #f0fdfa;
            color: #134e4a;
        }
        .quiz-option {
            transition: all 0.2s ease-in-out;
        }
        .quiz-option.selected {
            background-color: #ccfbf1;
            border-color: #14b8a6;
        }
        .quiz-option.correct {
            background-color: #d1fae5;
            border-color: #10b981;
        }
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #f87171;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 40vh;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div id="splash-screen" class="fixed inset-0 bg-white z-50 flex flex-col items-center justify-center">
        <div class="text-center">
            <div class="text-6xl mb-4">📖</div>
            <h1 class="text-4xl font-bold text-teal-800 mb-2">النحو الكافي</h1>
            <p class="text-lg text-gray-600">تعلم النحو العربي ببساطة</p>
            <button id="start-app-btn" class="mt-8 px-8 py-3 bg-teal-600 text-white font-bold rounded-full shadow-lg hover:bg-teal-700 transition-transform transform hover:scale-105">
                ابدأ الآن
            </button>
        </div>
    </div>

    <div id="app-container" class="pb-20">
        <header class="sticky top-0 bg-white/80 backdrop-blur-sm z-30 shadow-sm">
            <div class="container mx-auto px-4 py-3 flex justify-between items-center">
                <h1 class="text-2xl font-bold text-teal-800">النحو الكافي</h1>
                <button class="text-gray-600 hover:text-teal-600 text-2xl">
                    ⚙️
                </button>
            </div>
        </header>

        <main class="container mx-auto p-4 md:p-6">
            
            <div id="home-page" class="page active">
                <h2 class="text-2xl font-bold mb-4 text-gray-700">أبواب النحو</h2>
                <div id="lessons-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                </div>
            </div>

            <div id="lesson-page" class="page">
            </div>

            <div id="search-page" class="page">
                <h2 class="text-2xl font-bold mb-4 text-gray-700">🔍 بحث نحوي</h2>
                <div class="relative">
                    <input type="text" id="search-input" placeholder="ابحث عن مصطلح أو قاعدة..." class="w-full p-3 pr-10 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
                        🔍
                    </div>
                </div>
                <div id="search-results" class="mt-4 space-y-3">
                    <p class="text-center text-gray-500">ابدأ الكتابة لعرض النتائج...</p>
                </div>
            </div>

            <div id="progress-page" class="page">
                 <h2 class="text-2xl font-bold mb-4 text-gray-700">📊 مستواي</h2>
                 <div class="bg-white p-6 rounded-xl shadow-md">
                     <p class="text-center text-lg text-gray-600 mb-2">أحسنت! هذا هو ملخص تقدمك.</p>
                     <h3 class="text-center text-3xl font-bold text-teal-700 mb-6">مستوى: متقدم</h3>
                     <div class="chart-container">
                         <canvas id="progress-chart"></canvas>
                     </div>
                     <div class="mt-6 flex justify-around text-center">
                         <div>
                             <p class="text-2xl font-bold text-gray-800">١٢</p>
                             <p class="text-sm text-gray-500">درس مكتمل</p>
                         </div>
                         <div>
                             <p class="text-2xl font-bold text-gray-800">٨٥٪</p>
                             <p class="text-sm text-gray-500">متوسط الدرجات</p>
                         </div>
                         <div>
                             <p class="text-2xl font-bold text-gray-800">٤</p>
                             <p class="text-sm text-gray-500">شارات مكتسبة</p>
                         </div>
                     </div>
                 </div>
            </div>

            <div id="arena-page" class="page">
                <h2 class="text-2xl font-bold mb-4 text-gray-700">🏛️ الساحة الثقافية</h2>
                <div class="space-y-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <h3 class="font-bold text-lg text-teal-800">أثر النحو في فهم القرآن</h3>
                        <p class="text-gray-600 mt-1">مقالة قصيرة تستعرض كيف يساهم علم النحو في تدبر آيات القرآن الكريم.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <h3 class="font-bold text-lg text-teal-800">اختبار ثقافي: أمثال عربية</h3>
                        <p class="text-gray-600 mt-1">اختبر معرفتك بالأمثال العربية الشهيرة وإعرابها.</p>
                    </div>
                </div>
            </div>

        </main>

        <nav class="fixed bottom-0 right-0 left-0 bg-white/90 backdrop-blur-sm shadow-t-md z-30 border-t border-gray-200">
            <div class="flex justify-around">
                <button data-page="home-page" class="bottom-nav-btn flex-1 py-3 px-2 text-center text-gray-600 hover:text-teal-600 transition active">
                    <span class="text-2xl">📚</span>
                    <span class="block text-xs font-medium">الأبواب</span>
                </button>
                <button data-page="search-page" class="bottom-nav-btn flex-1 py-3 px-2 text-center text-gray-600 hover:text-teal-600 transition">
                    <span class="text-2xl">🔍</span>
                    <span class="block text-xs font-medium">بحث</span>
                </button>
                <button data-page="progress-page" class="bottom-nav-btn flex-1 py-3 px-2 text-center text-gray-600 hover:text-teal-600 transition">
                    <span class="text-2xl">📊</span>
                    <span class="block text-xs font-medium">مستواي</span>
                </button>
                 <button data-page="arena-page" class="bottom-nav-btn flex-1 py-3 px-2 text-center text-gray-600 hover:text-teal-600 transition">
                    <span class="text-2xl">🏛️</span>
                    <span class="block text-xs font-medium">الساحة</span>
                </button>
            </div>
        </nav>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const lessonsData = [
                { id: 1, title: 'الجملة الاسمية', icon: '📝', content: 'هي الجملة التي تبدأ باسم، وتتكون من ركنين أساسيين هما المبتدأ والخبر. مثال: السماءُ صافيةٌ.' },
                { id: 2, title: 'الجملة الفعلية', icon: '🏃‍♂️', content: 'هي الجملة التي تبدأ بفعل، وتتكون من ركنين أساسيين هما الفعل والفاعل. مثال: كتبَ الطالبُ الدرسَ.' },
                { id: 3, title: 'كان وأخواتها', icon: '🔄', content: 'أفعال ناقصة ناسخة تدخل على الجملة الاسمية فترفع المبتدأ ويسمى اسمها، وتنصب الخبر ويسمى خبرها.' },
                { id: 4, title: 'إن وأخواتها', icon: '⚖️', content: 'حروف ناسخة تدخل على الجملة الاسمية فتنصب المبتدأ ويسمى اسمها، وترفع الخبر ويسمى خبرها.' },
                { id: 5, title: 'الفاعل ونائبه', icon: '🧍', content: 'الفاعل هو من قام بالفعل. ونائب الفاعل يحل محل الفاعل عند بناء الفعل للمجهول.' },
                { id: 6, title: 'المفعول به', icon: '🎯', content: 'اسم منصوب يدل على من وقع عليه فعل الفاعل.' },
                { id: 7, title: 'الظروف', icon: '⏰', content: 'ظرف الزمان والمكان اسمان منصوبان يدلان على زمان أو مكان وقوع الفعل.' },
                { id: 8, title: 'الحال', icon: '🎭', content: 'اسم نكرة منصوب يبين هيئة صاحبه عند وقوع الفعل.' },
            ];
            
            const quizData = {
                1: [
                    { q: 'ما هو المبتدأ في جملة "العلمُ نورٌ"؟', a: ['نورٌ', 'العلمُ', 'هو'], c: 1 },
                    { q: 'الجملة الاسمية تتكون من...', a: ['فعل وفاعل', 'مبتدأ وخبر', 'جار ومجرور'], c: 1 },
                ],
                2: [
                     { q: 'حدد الفعل في جملة "يقرأُ محمدٌ الكتابَ".', a: ['محمدٌ', 'الكتابَ', 'يقرأُ'], c: 2 },
                ]
            };
            
            const i3rabData = {
                1: [
                    { sentence: 'السماءُ صافيةٌ.', analysis: '<b>السماءُ:</b> مبتدأ مرفوع وعلامة رفعه الضمة الظاهرة على آخره.<br><b>صافيةٌ:</b> خبر مرفوع وعلامة رفعه تنوين الضم الظاهر على آخره.' },
                ],
                2: [
                    { sentence: 'كتبَ الطالبُ الدرسَ.', analysis: '<b>كتبَ:</b> فعل ماضٍ مبني على الفتح.<br><b>الطالبُ:</b> فاعل مرفوع وعلامة رفعه الضمة الظاهرة على آخره.<br><b>الدرسَ:</b> مفعول به منصوب وعلامة نصبه الفتحة الظاهرة على آخره.' },
                ]
            };

            const searchTerms = [
                { term: 'فاعل', def: 'اسم مرفوع يدل على من قام بالفعل.' },
                { term: 'مفعول به', def: 'اسم منصوب يدل على من وقع عليه فعل الفاعل.' },
                { term: 'مبتدأ', def: 'الاسم الذي تبدأ به الجملة الاسمية.' },
                { term: 'خبر', def: 'الجزء المتمم لمعنى المبتدأ في الجملة الاسمية.' },
                { term: 'إعراب', def: 'تغير حركة آخر الكلمة حسب موقعها في الجملة.' },
            ];

            const splashScreen = document.getElementById('splash-screen');
            const startAppBtn = document.getElementById('start-app-btn');
            const lessonsGrid = document.getElementById('lessons-grid');
            const pages = document.querySelectorAll('.page');
            const navButtons = document.querySelectorAll('.bottom-nav-btn');
            const lessonPageContainer = document.getElementById('lesson-page');
            const searchInput = document.getElementById('search-input');
            const searchResultsContainer = document.getElementById('search-results');

            function showPage(pageId) {
                pages.forEach(p => p.classList.remove('active'));
                document.getElementById(pageId).classList.add('active');
                
                navButtons.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.page === pageId);
                });
                
                window.scrollTo(0, 0);
            }

            function renderHomePage() {
                lessonsGrid.innerHTML = '';
                lessonsData.forEach(lesson => {
                    const card = document.createElement('div');
                    card.className = 'bg-white p-4 rounded-xl shadow-md cursor-pointer hover:shadow-lg hover:-translate-y-1 transition-all flex flex-col items-center text-center';
                    card.innerHTML = `<div class="text-4xl mb-3">${lesson.icon}</div><h3 class="font-bold text-lg text-gray-800">${lesson.title}</h3>`;
                    card.addEventListener('click', () => renderLessonPage(lesson.id));
                    lessonsGrid.appendChild(card);
                });
            }

            function renderLessonPage(lessonId) {
                const lesson = lessonsData.find(l => l.id === lessonId);
                lessonPageContainer.innerHTML = `
                    <button id="back-to-home" class="mb-4 text-teal-600 font-semibold hover:text-teal-800">→ العودة إلى الأبواب</button>
                    <h2 class="text-3xl font-bold mb-2 text-gray-800">${lesson.title}</h2>
                    <p class="text-gray-500 mb-6">${lesson.content}</p>
                    
                    <div class="flex border-b-2 border-gray-200 mb-4">
                        <button data-tab="explanation" class="lesson-tab py-2 px-4 -mb-0.5 border-b-2 border-transparent font-semibold text-gray-600 hover:text-teal-700 active">🧾 الشرح</button>
                        <button data-tab="quiz" class="lesson-tab py-2 px-4 -mb-0.5 border-b-2 border-transparent font-semibold text-gray-600 hover:text-teal-700">🧠 اختبر نفسك</button>
                        <button data-tab="i3rab" class="lesson-tab py-2 px-4 -mb-0.5 border-b-2 border-transparent font-semibold text-gray-600 hover:text-teal-700">🧮 إعراب أمثلة</button>
                    </div>

                    <div id="lesson-content"></div>
                `;
                showPage('lesson-page');

                document.getElementById('back-to-home').addEventListener('click', () => showPage('home-page'));
                
                const lessonTabs = lessonPageContainer.querySelectorAll('.lesson-tab');
                lessonTabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        lessonTabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');
                        renderLessonContent(tab.dataset.tab, lessonId);
                    });
                });
                
                renderLessonContent('explanation', lessonId);
            }
            
            function renderLessonContent(tabName, lessonId) {
                const contentContainer = document.getElementById('lesson-content');
                const lesson = lessonsData.find(l => l.id === lessonId);
                
                switch(tabName) {
                    case 'explanation':
                        contentContainer.innerHTML = `<div class="bg-white p-6 rounded-lg shadow-inner"><p class="text-lg leading-relaxed">${lesson.content}</p></div>`;
                        break;
                    case 'quiz':
                        renderQuiz(lessonId);
                        break;
                    case 'i3rab':
                        const examples = i3rabData[lessonId] || [];
                        if (examples.length > 0) {
                            contentContainer.innerHTML = examples.map(ex => `
                                <div class="bg-white p-5 rounded-lg shadow-inner mb-4">
                                    <p class="font-mono text-xl mb-3 text-teal-800">${ex.sentence}</p>
                                    <div class="border-t pt-3 text-gray-700 leading-loose">${ex.analysis}</div>
                                </div>
                            `).join('');
                        } else {
                            contentContainer.innerHTML = `<p class="text-center text-gray-500 p-6">لا توجد أمثلة إعرابية لهذا الدرس بعد.</p>`;
                        }
                        break;
                }
            }

            function renderQuiz(lessonId) {
                const quizContainer = document.getElementById('lesson-content');
                const questions = quizData[lessonId];
                if (!questions || questions.length === 0) {
                     quizContainer.innerHTML = `<p class="text-center text-gray-500 p-6">لا يوجد اختبار لهذا الدرس بعد.</p>`;
                     return;
                }

                let currentQuestionIndex = 0;
                let score = 0;
                let selectedOption = null;

                function showQuestion() {
                    selectedOption = null;
                    const question = questions[currentQuestionIndex];
                    quizContainer.innerHTML = `
                        <div class="bg-white p-6 rounded-lg shadow-inner">
                            <p class="text-lg font-semibold mb-4">${currentQuestionIndex + 1}. ${question.q}</p>
                            <div class="space-y-3">
                                ${question.a.map((option, index) => `
                                    <div class="quiz-option border-2 border-gray-200 rounded-lg p-3 cursor-pointer" data-index="${index}">${option}</div>
                                `).join('')}
                            </div>
                            <div id="quiz-feedback" class="mt-4"></div>
                            <button id="check-answer" class="mt-4 w-full bg-teal-600 text-white font-bold py-2 rounded-lg hover:bg-teal-700 transition disabled:bg-gray-400" disabled>تحقق</button>
                        </div>
                    `;
                    
                    const options = quizContainer.querySelectorAll('.quiz-option');
                    options.forEach(opt => {
                        opt.addEventListener('click', () => {
                            options.forEach(o => o.classList.remove('selected'));
                            opt.classList.add('selected');
                            selectedOption = parseInt(opt.dataset.index);
                            document.getElementById('check-answer').disabled = false;
                        });
                    });

                    document.getElementById('check-answer').addEventListener('click', checkAnswer);
                }
                
                function checkAnswer() {
                    this.disabled = true;
                    const question = questions[currentQuestionIndex];
                    const feedbackEl = document.getElementById('quiz-feedback');
                    const options = quizContainer.querySelectorAll('.quiz-option');

                    options[question.c].classList.add('correct');
                    if (selectedOption === question.c) {
                        score++;
                        feedbackEl.innerHTML = `<p class="text-green-600 font-bold">إجابة صحيحة!</p>`;
                    } else {
                        options[selectedOption].classList.add('incorrect');
                         feedbackEl.innerHTML = `<p class="text-red-600 font-bold">إجابة خاطئة. الصحيح هو: ${question.a[question.c]}</p>`;
                    }

                    setTimeout(() => {
                        currentQuestionIndex++;
                        if (currentQuestionIndex < questions.length) {
                            showQuestion();
                        } else {
                            showResults();
                        }
                    }, 2000);
                }

                function showResults() {
                    quizContainer.innerHTML = `
                        <div class="bg-white p-8 rounded-lg shadow-inner text-center">
                            <h3 class="text-2xl font-bold mb-3">انتهى الاختبار!</h3>
                            <p class="text-lg text-gray-700">نتيجتك هي:</p>
                            <p class="text-4xl font-bold my-4 text-teal-700">${score} من ${questions.length}</p>
                             <p class="text-lg text-gray-700">نسبة النجاح: ${Math.round((score / questions.length) * 100)}%</p>
                            <button id="retake-quiz" class="mt-6 bg-teal-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-teal-700 transition">أعد الاختبار</button>
                        </div>
                    `;
                    document.getElementById('retake-quiz').addEventListener('click', () => renderQuiz(lessonId));
                }

                showQuestion();
            }

            function handleSearch(query) {
                if (query.trim() === '') {
                    searchResultsContainer.innerHTML = `<p class="text-center text-gray-500">ابدأ الكتابة لعرض النتائج...</p>`;
                    return;
                }
                const results = searchTerms.filter(item => item.term.includes(query));
                if (results.length > 0) {
                     searchResultsContainer.innerHTML = results.map(item => `
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-bold text-lg">${item.term}</h3>
                            <p class="text-gray-600">${item.def}</p>
                        </div>
                     `).join('');
                } else {
                    searchResultsContainer.innerHTML = `<p class="text-center text-gray-500">لم يتم العثور على نتائج لـ "${query}"</p>`;
                }
            }
            
            function renderProgressChart() {
                const ctx = document.getElementById('progress-chart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['مكتمل', 'متبقي'],
                        datasets: [{
                            label: 'التقدم',
                            data: [85, 15],
                            backgroundColor: [
                                '#14b8a6',
                                '#f0f0f0'
                            ],
                            borderColor: [
                                '#ffffff',
                                '#ffffff'
                            ],
                            borderWidth: 4,
                            cutout: '70%'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: false
                            }
                        }
                    }
                });
            }


            startAppBtn.addEventListener('click', () => {
                splashScreen.style.opacity = '0';
                splashScreen.style.visibility = 'hidden';
            });
            setTimeout(() => {
                splashScreen.style.opacity = '0';
                splashScreen.style.visibility = 'hidden';
            }, 2500);

            navButtons.forEach(button => {
                button.addEventListener('click', () => {
                    showPage(button.dataset.page);
                });
            });

            searchInput.addEventListener('keyup', (e) => handleSearch(e.target.value));

            renderHomePage();
            renderProgressChart();
        });
    </script>
</body>
</html>
